<script setup lang="ts">
import { getBookCatalogTree, getBookList, getResourceTree } from '@/api/aiTask'
import { useAiTaskStore } from '@/stores/modules/aiTask'

const route = useRoute()
const router = useRouter()

const aiTaskStore = useAiTaskStore()
let showTree = $ref<boolean>(false)
let showLoading = $ref<boolean>(false)
let courseLoading = $ref<boolean>(true)

let currentBookName = $ref<string>('')// 当前书名
let treeData = $ref<any>('') // 树形结构数据
let bookCatalogId = $ref<any>(null) // 当前选中章节ID
let bookCatalogName = $ref<any>(null) // 当前选中章节名称
let catalogCourseType = $ref<any>(null) // 当前选中章节类型
let currentBookId = $ref<any>(null) // 当前选中书籍ID
let bookList = $ref<any>([]) // 书籍列表
let resourceList = $ref<any>([]) // 当前选中章节资源列表
const studentData = inject<Ref<any>>('studentData', ref({}))

// 获取图片地址
let getImgSrc = $computed(() => {
  return (it) => {
    const typeMap = {
      4: 'knowledgeVideo',
      5: 'note',
    }
    const tempImg = typeMap[it.catalogCourseType] || 'course'
    return $g.tool.getFileUrl(`taskCenter/${tempImg}.png`)
  }
})
/* 点击章节 */
async function nodeClick(node) {
  if (!node.children.length) {
    bookCatalogId = node.bookCatalogId
    bookCatalogName = node.bookCatalogName
    catalogCourseType = node.catalogCourseType
    await getResourceTreeApi()
  }
}
/* 获取校本资源章节树 */
async function fetchSchoolResourceTree() {
  try {
    treeData = []
    showLoading = true
    let res = await getBookCatalogTree({
      bookId: currentBookId,
    })
    treeData = res ?? []
    setDefaultNode(treeData?.[0])
    await getResourceTreeApi()

    showTree = true
    showLoading = false
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (err) {
    showLoading = false
    courseLoading = false
    console.log(err)
  }
}
/* 设置默认tree节点 */
function setDefaultNode(obj) {
  while (obj && Array.isArray(obj.children) && obj.children.length)
    obj = obj.children[0]

  if (obj) {
    bookCatalogId = obj.bookCatalogId ?? null
    bookCatalogName = obj.bookCatalogName ?? ''
    catalogCourseType = obj.catalogCourseType
  }
}
/* 启鸣资源-获取教材版本 */
async function fetchBook() {
  try {
    showLoading = true
    let res = await getBookList({
      sysSubjectId: route.query?.subjectId,
    })
    bookList = res ?? []
    currentBookId = bookList.length ? res[0].bookId : null
    currentBookName = bookList.length ? res[0].bookName : ''
    if (currentBookId) {
      await fetchSchoolResourceTree()
    }
    else {
      showLoading = false
      courseLoading = false
    }
  }
  catch (err) {
    showLoading = false
    courseLoading = false
    console.log(err)
  }
}
/* 点击书籍 */
async function handleChangeBook(item) {
  currentBookId = item.bookId
  currentBookName = item.bookName
  showTree = false
  resourceList = []
  await fetchSchoolResourceTree()
}
async function getResourceTreeApi(catalogModificationList = []) {
  try {
    if (!bookCatalogId) {
      courseLoading = false
      return
    }
    courseLoading = true
    let params: any = { bookCatalogId }
    if (catalogModificationList.length)
      params.catalogModificationList = catalogModificationList

    let res = await getResourceTree(params)
    resourceList = res
    courseLoading = false
  }
  catch (error) {
    courseLoading = false
    console.log(error)
  }
}
// 格式化描述
function formatDesc(obj: any) {
  if (!obj.resourceList?.length) return ''
  const typeMap: Record<number, string> = {
    1: '道题',
    2: '个卡片',
    3: '个视频',
    4: '个课件',
  }
  const result: string[] = obj.resourceList
    .filter(item => item.resourceNum > 0 && typeMap[item.resourceType])
    .map(item => `${item.resourceNum}${typeMap[item.resourceType]}`)
  if (obj.computeDuration && obj.computeDuration > 0)
    result.push(`约${Math.ceil(obj.computeDuration / 60)}分钟`)

  return `(${result.join('，')})`
}
// 加入资源
function addResource(obj?) {
  if (obj?.bookCatalogId) {
    const selected = studentData.value.selectedResource
    const index = selected.findIndex(item => item?.bookCatalogId === bookCatalogId)
    if (index !== -1) {
      const selectedResourceList = selected[index].selectedResourceList || []
      if (!selectedResourceList.some(item => item.bookCatalogId === obj.bookCatalogId)) {
        selected[index].selectedResourceList = [...selectedResourceList,{
            ...obj,
            selectedStatus: true,
          }]
      }
    }
    else {
      if (selected.filter(it => it.bookCatalogId).length >= 1) {
        $g.confirm({
          content: '只能选择一个章节的课程，是否需要替换已选章节的课程？',
        }).then(async () => {
          selected.length = 0
          selected.push({
            bookCatalogId,
            bookCatalogName,
            selectedResourceList: [{
              ...obj,
              selectedStatus: true,
            }],
          })
        }).catch(() => {
          })
      }
      else {
        selected.push({
          bookCatalogId,
          bookCatalogName,
          selectedResourceList: [{
            ...obj,
            selectedStatus: true,
          }],
        })
      }
    }
  }
  else {
    const selected = studentData.value.selectedResource
    const index = selected.findIndex(item => item?.bookCatalogId === bookCatalogId)
    if (index !== -1) {
      const selectedResourceList = selected[index].selectedResourceList ?? []
      const existingIds = new Set(selectedResourceList.map(item => item.bookCatalogId))
      // 合并新旧资源，避免重复
      // 优化：只合并未存在的资源，避免重复
      const mergedResources = [
        ...selectedResourceList,
        ...resourceList
          ?.filter(item => !existingIds.has(item.bookCatalogId))
          ?.map(it => ({
            ...it,
            selectedStatus: true,
          })),
      ]
      selected[index].selectedResourceList = mergedResources
    }
    else {
      if (selected.filter(it => it.bookCatalogId).length >= 1) {
        $g.confirm({
          content: '只能选择一个章节的课程，是否需要替换已选章节的课程？',
        }).then(async () => {
          selected.length = 0
          selected.push({
            bookCatalogId,
            bookCatalogName,
            selectedResourceList: resourceList.map(it => ({
              ...it,
              selectedStatus: true,
            })),
          })
        }).catch(() => {
          })
      }
      else {
        selected.push({
          bookCatalogId,
          bookCatalogName,
          selectedResourceList: resourceList.map(it => ({
            ...it,
            selectedStatus: true,
          })),
        })
      }
    }
  }
}
// 取消加入
function deleteResource(obj?) {
  const selected = studentData.value.selectedResource
  const index = selected.findIndex(item => item?.bookCatalogId === bookCatalogId)
  if (obj?.bookCatalogId) {
    if (index !== -1) {
      const resourceListArr = selected[index].selectedResourceList || []
      selected[index].selectedResourceList = resourceListArr.filter(it => it.bookCatalogId !== obj.bookCatalogId)
      // 如果该目录下已无资源，移除整个目录对象
      if (selected[index].selectedResourceList.length === 0)
        selected.splice(index, 1)
    }
  }
  else {
    selected[index] = []
  }
}
// 判断单个资源是否已加入
function isAdd(obj) {
  let currentBook = studentData.value.selectedResource?.find(its => its?.bookCatalogId == bookCatalogId)
  if (currentBook)
    return currentBook?.selectedResourceList?.some(it => it.bookCatalogId == obj.bookCatalogId)

  return false
}
// 判断选中知识点下所有资源是否全部加入
function isAddAll() {
  if (!Array.isArray(resourceList) || resourceList.length === 0) return false
  const currentBook = studentData.value.selectedResource?.find(its => its?.bookCatalogId === bookCatalogId)
  if (!currentBook || !Array.isArray(currentBook.selectedResourceList)) return false
  // 用 Set 优化查找
  const selectedIds = new Set(currentBook.selectedResourceList.map(item => item.bookCatalogId))
  return resourceList.every(res => selectedIds.has(res.bookCatalogId))
}
// 编辑课程
function editCourse(obj) {
  router.push({
    name: 'AiTaskEdit',
    query: {
      bookCatalogId: obj.bookCatalogId,
      bookId: currentBookId,
      layerType: studentData.value.layerType,
      ...route.query,
    },
  })
}
watch(() => currentBookId, (newVal) => {
  if (newVal) studentData.value.bookId = newVal
}, {
  immediate: true,
})
onMounted(() => {
  fetchBook()
})
onActivated(() => {
  let catalogModificationList = Object.entries(aiTaskStore?.modifyMap)?.map(([bookCatalogId, selectModification]) => ({
    bookCatalogId,
    selectModification,
  })) ?? []
  let catalogIds = Object.keys(aiTaskStore?.modifyMap)
  // 缓存值为空
  if (!catalogModificationList?.length) return
  // 缓存值不在展示列表中
  if (!resourceList.some(it => catalogIds.includes(it.bookCatalogId?.toString()))) return
  getResourceTreeApi(catalogModificationList as any)
})
</script>

<template>
  <div>
    <div class="flex  h-[calc(100vh-250px)] ">
      <div class="w-[399px] overflow-hidden bg-white p-15px mr-26px  rounded-[6px] ">
        <div class="text-[15px] font-600 leading-21px border-b border-[#EEEEEE] pb-13px mb-13px">
          课程内容
        </div>
        <g-loading v-if="showLoading" class="h-200px"></g-loading>
        <template v-else>
          <div v-if="!showTree" class="h-[calc(100%-60px)] overflow-y-auto ">
            <g-empty v-if="!bookList.length"></g-empty>
            <div
              v-for="item in bookList"
              :id="item.bookId"
              :key="item.bookId"
              class="text-14px text-[#333] font-600 py-6px px-7px flex items-start justify-between cursor-pointer rounded-[6px] mb-9px"
              :class="{
                'text-[#6474FD] bg-[rgba(100,116,253,0.1)]':
                  currentBookId == item.bookId,
              }"
              @click="handleChangeBook(item)"
            >
              <div>{{ item.bookName }}</div>
              <img
                :src="
                  currentBookId == item.bookId
                    ? $g.tool.getFileUrl('taskCenter/clicked-right.png')
                    : $g.tool.getFileUrl('taskCenter/not-clicked-right.png')
                "
                class="w-19px h-19px mt-2px"
              />
            </div>
          </div>
          <div v-else class="h-[calc(100%-60px)] overflow-y-auto ">
            <div class="flex items-start mb-10px" @click="showTree = false">
              <svg-common-back
                class="w-[19px] h-19px mt-1px mr-2px cursor-pointer"
              />
              <div class="font-600 text-14px cursor-pointer ">
                {{ currentBookName }}
              </div>
            </div>
            <div class="h-[calc(100%-40px)] overflow-auto ">
              <g-tree
                v-if="treeData.length"
                ref="Tree2Ref"
                :border="false"
                tree-name="RightTree"
                node-key="bookCatalogId"
                class="text-[13px]"
                :default-expanded-keys="[bookCatalogId]"
                :default-checked-keys="[bookCatalogId]"
                :current-node-key="bookCatalogId"
                :tree-data="treeData"
                check-strictly
                :highlight-check="false"
                :props="{
                  disabled: (data, node) => !!(data.children && data.children.length),
                }"
                @node-click="nodeClick"
                @node-expand="
                  () => {
                    $g.tool.renderMathjax()
                  }
                "
              >
                <template #body="{ data }">
                  <div class="flex items-center">
                    <div v-if="data.catalogCourseType == 2" class="!w-[44px] h-fit text-center bg-[#6474FD] br-[4px]  text-[#fff] mr-[6px] text-[12px]">
                      AI课
                    </div>
                    <g-mathjax class="flex-1" :text="data.bookCatalogName" />
                  </div>
                </template>
              </g-tree>
              <g-empty v-else></g-empty>
            </div>
          </div>
        </template>
      </div>
      <div
        class=" flex-1  h-full    overflow-y-auto  py-[4px] "
      >
        <div v-if="resourceList.length" class="flex justify-between">
          <div class="flex">
            <div v-if="catalogCourseType == 2" class="w-fit h-fit  bg-[#6474FD] br-[4px] px-[11px] text-[#fff] mr-[11px]">
              AI课
            </div>
            <div class="text-[19px] text-[#333] leading-[26px] font-600">
              {{ bookCatalogName }}
            </div>
          </div>

          <el-button
            v-if="!isAddAll()"
            type="primary"
            plain
            @click="addResource"
          >
            全部加入
          </el-button>
          <el-button
            v-else
            type="danger"
            plain
            @click="deleteResource"
          >
            全部取消
          </el-button>
        </div>
        <g-loading v-if="courseLoading" class="h-200px"></g-loading>
        <div v-else class="h-[calc(100%-45px)] ">
          <g-empty v-if="!resourceList.length"></g-empty>
          <template v-for="it in resourceList" :key="it.bookCatalogId">
            <div
              v-if="it.catalogCourseType == 5"
              class="my-39px"
              style="border-top: 1px dashed #BDBDBD;"
            ></div>
            <div class="flex  justify-between items-center px-12px py-6px br-[11px] bg-[#fff] mt-13px ">
              <div class="flex">
                <img
                  :src="getImgSrc(it)"
                  alt=""
                  srcset=""
                  class="w-19px h-19px flex-shrink-0 mr-[9px]"
                >
                <div>
                  <div class="text-15px text-[#333] font-500 leading-[19px] mb-6px">
                    {{ it.bookCatalogName }}<span class="pl-8px">{{ formatDesc(it) }}</span>
                  </div>
                  <div class="text-10px text-[#9E9E9E] leading-[11px]">
                    {{ it.useCount ? `布置历史${it.useCount}次` : '无布置历史' }}
                  </div>
                </div>
              </div>
              <div class="flex">
                <el-button
                  type="info"
                  plain
                  @click="editCourse(it)"
                >
                  编辑课程
                </el-button>
                <el-button
                  v-if="!isAdd(it)"
                  type="primary"
                  plain
                  @click="addResource(it)"
                >
                  加入
                </el-button>
                <el-button
                  v-else
                  type="danger"
                  plain
                  @click="deleteResource(it)"
                >
                  取消加入
                </el-button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  :deep() {
  .el-tree-node__content {
    height: auto;
    padding: 5px 0;
    margin-bottom: 5px;
    background-color: transparent !important;
    transition: color 0.1s linear;
    border-radius: 6px;
  }
  .custom-tree-node {
    overflow: hidden;
    white-space: normal;
  }
  .is-checked {
    .el-tree-node__content {
      background-color: rgb(100 116 253 / 10%) !important;
      color: #6474fd;
      .expanded {
        color: #6474fd;
      }
      .el-icon {
        color: #6474fd;
      }
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
        color: #6c6c74 !important;
        .expanded {
          color: #6c6c74 !important;
        }
        .el-icon {
          color: #676a88 !important;
        }
      }
    }
  }
}
</style>
